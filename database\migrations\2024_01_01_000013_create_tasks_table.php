<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول المهام
     */
    public function up(): void
    {
        Schema::create('tasks', function (Blueprint $table) {
            $table->id();
            $table->string('title'); // عنوان المهمة
            $table->text('description')->nullable(); // وصف المهمة
            $table->foreignId('assigned_to')->constrained('users')->onDelete('cascade'); // المكلف بالمهمة
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade'); // منشئ المهمة
            $table->foreignId('case_id')->nullable()->constrained()->onDelete('cascade'); // القضية المرتبطة
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('cascade'); // العميل المرتبط
            $table->enum('type', [
                'research', // بحث قانوني
                'document_preparation', // إعداد مستندات
                'court_appearance', // حضور جلسة
                'client_meeting', // اجتماع عميل
                'follow_up', // متابعة
                'deadline', // موعد نهائي
                'reminder', // تذكير
                'administrative', // إدارية
                'other' // أخرى
            ])->default('other');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->enum('status', [
                'pending', // معلقة
                'in_progress', // قيد التنفيذ
                'completed', // مكتملة
                'cancelled', // ملغية
                'overdue' // متأخرة
            ])->default('pending');
            $table->datetime('due_date')->nullable(); // تاريخ الاستحقاق
            $table->datetime('completed_at')->nullable(); // تاريخ الإنجاز
            $table->text('completion_notes')->nullable(); // ملاحظات الإنجاز
            $table->integer('estimated_hours')->nullable(); // الساعات المقدرة
            $table->integer('actual_hours')->nullable(); // الساعات الفعلية
            $table->json('attachments')->nullable(); // المرفقات
            $table->json('checklist')->nullable(); // قائمة مراجعة
            $table->boolean('is_recurring')->default(false); // مهمة متكررة
            $table->json('recurrence_pattern')->nullable(); // نمط التكرار
            $table->boolean('send_reminder')->default(true); // إرسال تذكير
            $table->datetime('reminder_at')->nullable(); // وقت التذكير
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['assigned_to', 'status']);
            $table->index(['tenant_id', 'due_date']);
            $table->index(['tenant_id', 'priority']);
            $table->index(['case_id', 'status']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('tasks');
    }
};