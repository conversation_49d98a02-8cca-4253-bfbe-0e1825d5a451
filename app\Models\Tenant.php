<?php

namespace App\Models;

use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;
use Stancl\Tenancy\Contracts\TenantWithDatabase;
use Stancl\Tenancy\Database\Concerns\HasDatabase;
use Stancl\Tenancy\Database\Concerns\HasDomains;

/**
 * نموذج المستأجر - إدارة المستأجرين في النظام متعدد المستأجرين
 */
class Tenant extends BaseTenant implements TenantWithDatabase
{
    use HasDatabase, HasDomains;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'id',
        'name',
        'domain',
        'database',
        'data',
        'trial_ends_at',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'data' => 'array',
        'trial_ends_at' => 'datetime',
    ];

    /**
     * إنشاء مستأجر جديد مع قاعدة بيانات
     */
    public static function createWithDatabase(array $attributes): self
    {
        $tenant = static::create($attributes);
        $tenant->createDatabase();
        $tenant->run(function () {
            // تشغيل Migration للمستأجر الجديد
            \Artisan::call('migrate', [
                '--database' => 'tenant',
                '--force' => true,
            ]);
            
            // تشغيل Seeders الأساسية
            \Artisan::call('db:seed', [
                '--database' => 'tenant',
                '--class' => 'TenantSeeder',
                '--force' => true,
            ]);
        });

        return $tenant;
    }

    /**
     * الحصول على الاشتراك النشط
     */
    public function activeSubscription()
    {
        return $this->hasOne(Subscription::class)->where('status', 'active');
    }

    /**
     * جميع الاشتراكات
     */
    public function subscriptions()
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * التحقق من انتهاء فترة التجربة
     */
    public function isTrialExpired(): bool
    {
        return $this->trial_ends_at && $this->trial_ends_at->isPast();
    }

    /**
     * التحقق من وجود اشتراك نشط
     */
    public function hasActiveSubscription(): bool
    {
        return $this->activeSubscription()->exists();
    }

    /**
     * الحصول على حدود الاستخدام
     */
    public function getUsageLimits(): array
    {
        $subscription = $this->activeSubscription;
        
        if (!$subscription) {
            return [
                'max_users' => 1,
                'max_cases' => 10,
                'max_clients' => 10,
                'max_storage_gb' => 1,
            ];
        }

        return $subscription->plan->limits ?? [];
    }

    /**
     * التحقق من إمكانية إضافة مستخدم جديد
     */
    public function canAddUser(): bool
    {
        $limits = $this->getUsageLimits();
        $currentUsers = $this->run(function () {
            return User::count();
        });

        return $currentUsers < ($limits['max_users'] ?? 1);
    }

    /**
     * التحقق من إمكانية إضافة قضية جديدة
     */
    public function canAddCase(): bool
    {
        $limits = $this->getUsageLimits();
        $currentCases = $this->run(function () {
            return CaseModel::count();
        });

        return $currentCases < ($limits['max_cases'] ?? 10);
    }
}