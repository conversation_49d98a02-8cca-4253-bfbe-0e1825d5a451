<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول العملاء
     */
    public function up(): void
    {
        Schema::create('clients', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('email')->nullable();
            $table->string('phone');
            $table->string('phone_secondary')->nullable();
            $table->text('address')->nullable();
            $table->string('id_number')->nullable(); // رقم الهوية المدنية
            $table->string('passport_number')->nullable();
            $table->enum('client_type', ['individual', 'company', 'government'])->default('individual');
            $table->string('company_name')->nullable();
            $table->string('commercial_registration')->nullable();
            $table->date('birth_date')->nullable();
            $table->enum('gender', ['male', 'female'])->nullable();
            $table->string('nationality')->nullable();
            $table->string('occupation')->nullable();
            $table->text('notes')->nullable();
            $table->json('emergency_contacts')->nullable(); // جهات الاتصال الطارئة
            $table->enum('status', ['active', 'inactive', 'blacklisted'])->default('active');
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'client_type']);
            $table->index(['tenant_id', 'id_number']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('clients');
    }
};