<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول مكاتب المحاماة
     */
    public function up(): void
    {
        Schema::create('law_firms', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('logo')->nullable();
            $table->text('address');
            $table->string('phone');
            $table->string('email');
            $table->string('license_number')->unique();
            $table->string('commercial_registration')->nullable();
            $table->string('tax_number')->nullable();
            $table->string('website')->nullable();
            $table->text('description')->nullable();
            $table->json('specializations')->nullable(); // التخصصات القانونية
            $table->json('settings')->nullable(); // إعدادات المكتب
            $table->string('tenant_id');
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['tenant_id', 'status']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('law_firms');
    }
};