<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول أنواع القضايا
     */
    public function up(): void
    {
        Schema::create('case_types', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->text('description')->nullable();
            $table->string('color', 7)->default('#3B82F6'); // لون للتمييز
            $table->string('icon')->nullable(); // أيقونة
            $table->decimal('default_fee', 10, 2)->nullable(); // الرسوم الافتراضية
            $table->integer('estimated_duration_days')->nullable(); // المدة المتوقعة بالأيام
            $table->json('required_documents')->nullable(); // المستندات المطلوبة
            $table->boolean('is_active')->default(true);
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['tenant_id', 'is_active']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('case_types');
    }
};