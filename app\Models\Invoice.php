<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

/**
 * نموذج الفاتورة - إدارة الفواتير والمدفوعات
 */
class Invoice extends Model
{
    use HasFactory, SoftDeletes, LogsActivity;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'invoice_number',
        'client_id',
        'case_id',
        'created_by',
        'type',
        'issue_date',
        'due_date',
        'subtotal',
        'tax_percentage',
        'tax_amount',
        'discount_percentage',
        'discount_amount',
        'total_amount',
        'paid_amount',
        'balance',
        'status',
        'currency',
        'notes',
        'terms_conditions',
        'line_items',
        'payment_methods',
        'payment_link',
        'sent_at',
        'viewed_at',
        'paid_at',
        'reminder_count',
        'last_reminder_at',
        'tenant_id',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'issue_date' => 'date',
        'due_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_percentage' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_percentage' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'balance' => 'decimal:2',
        'line_items' => 'array',
        'payment_methods' => 'array',
        'sent_at' => 'datetime',
        'viewed_at' => 'datetime',
        'paid_at' => 'datetime',
        'last_reminder_at' => 'datetime',
    ];

    /**
     * إعدادات تسجيل الأنشطة
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['status', 'total_amount', 'paid_amount'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقة مع العميل
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * العلاقة مع القضية
     */
    public function case()
    {
        return $this->belongsTo(CaseModel::class, 'case_id');
    }

    /**
     * العلاقة مع منشئ الفاتورة
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * العلاقة مع تسجيل الوقت المفوتر
     */
    public function timesheets()
    {
        return $this->hasMany(Timesheet::class);
    }

    /**
     * العلاقة مع المصروفات المفوترة
     */
    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * حساب الضريبة تلقائياً
     */
    public function calculateTax(): void
    {
        $this->tax_amount = ($this->subtotal * $this->tax_percentage) / 100;
        $this->updateTotal();
    }

    /**
     * حساب الخصم تلقائياً
     */
    public function calculateDiscount(): void
    {
        $this->discount_amount = ($this->subtotal * $this->discount_percentage) / 100;
        $this->updateTotal();
    }

    /**
     * تحديث المجموع الإجمالي
     */
    public function updateTotal(): void
    {
        $this->total_amount = $this->subtotal + $this->tax_amount - $this->discount_amount;
        $this->balance = $this->total_amount - $this->paid_amount;
    }

    /**
     * تسجيل دفعة جديدة
     */
    public function recordPayment(float $amount, array $paymentData = []): Payment
    {
        $payment = $this->payments()->create(array_merge([
            'client_id' => $this->client_id,
            'amount' => $amount,
            'payment_date' => now()->toDateString(),
            'status' => 'completed',
            'tenant_id' => $this->tenant_id,
        ], $paymentData));

        $this->updatePaymentStatus();
        
        return $payment;
    }

    /**
     * تحديث حالة الدفع
     */
    public function updatePaymentStatus(): void
    {
        $this->paid_amount = $this->payments()->where('status', 'completed')->sum('amount');
        $this->balance = $this->total_amount - $this->paid_amount;

        if ($this->balance <= 0) {
            $this->status = 'paid';
            $this->paid_at = now();
        } elseif ($this->paid_amount > 0) {
            $this->status = 'partially_paid';
        } elseif ($this->due_date && $this->due_date->isPast()) {
            $this->status = 'overdue';
        }

        $this->save();
    }

    /**
     * إرسال الفاتورة للعميل
     */
    public function send(): void
    {
        $this->status = 'sent';
        $this->sent_at = now();
        $this->save();

        // إرسال إشعار للعميل
        // يمكن إضافة منطق الإرسال هنا
    }

    /**
     * تسجيل عرض الفاتورة
     */
    public function markAsViewed(): void
    {
        if (!$this->viewed_at) {
            $this->viewed_at = now();
            $this->status = 'viewed';
            $this->save();
        }
    }

    /**
     * إرسال تذكير
     */
    public function sendReminder(): void
    {
        $this->reminder_count++;
        $this->last_reminder_at = now();
        $this->save();

        // إرسال تذكير للعميل
        // يمكن إضافة منطق الإرسال هنا
    }

    /**
     * التحقق من كون الفاتورة مدفوعة
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * التحقق من كون الفاتورة متأخرة
     */
    public function isOverdue(): bool
    {
        return $this->due_date && $this->due_date->isPast() && !$this->isPaid();
    }

    /**
     * التحقق من كون الفاتورة مدفوعة جزئياً
     */
    public function isPartiallyPaid(): bool
    {
        return $this->status === 'partially_paid';
    }

    /**
     * الحصول على عدد الأيام المتبقية للاستحقاق
     */
    public function getDaysUntilDueAttribute(): ?int
    {
        if (!$this->due_date) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * إنشاء رقم فاتورة تلقائي
     */
    public static function generateInvoiceNumber(): string
    {
        $year = now()->year;
        $month = now()->format('m');
        $lastInvoice = static::whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();
        
        $number = $lastInvoice ? (int) substr($lastInvoice->invoice_number, -4) + 1 : 1;
        
        return sprintf('INV-%d%s-%04d', $year, $month, $number);
    }

    /**
     * Scope للفواتير المعلقة
     */
    public function scopePending($query)
    {
        return $query->whereIn('status', ['draft', 'sent', 'viewed']);
    }

    /**
     * Scope للفواتير المتأخرة
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
            ->orWhere(function ($q) {
                $q->whereIn('status', ['sent', 'viewed'])
                  ->where('due_date', '<', now());
            });
    }

    /**
     * Scope للفواتير المدفوعة
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }
}