<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول قوالب المستندات
     */
    public function up(): void
    {
        Schema::create('document_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم القالب
            $table->text('description')->nullable(); // وصف القالب
            $table->longText('content'); // محتوى القالب
            $table->enum('type', [
                'contract', // عقد
                'power_of_attorney', // توكيل
                'legal_notice', // إنذار قانوني
                'petition', // عريضة
                'memorandum', // مذكرة
                'agreement', // اتفاقية
                'letter', // خطاب
                'invoice', // فاتورة
                'receipt', // إيصال
                'report', // تقرير
                'other' // أخرى
            ])->default('other');
            $table->enum('category', [
                'civil', // مدني
                'commercial', // تجاري
                'criminal', // جنائي
                'family', // أسرة
                'administrative', // إداري
                'labor', // عمالي
                'real_estate', // عقاري
                'inheritance', // مواريث
                'general' // عام
            ])->default('general');
            $table->json('variables')->nullable(); // المتغيرات القابلة للتخصيص
            $table->json('required_fields')->nullable(); // الحقول المطلوبة
            $table->string('language', 2)->default('ar'); // لغة القالب
            $table->boolean('is_active')->default(true);
            $table->boolean('is_system_template')->default(false); // قالب نظام أم مخصص
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->integer('usage_count')->default(0); // عدد مرات الاستخدام
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['tenant_id', 'type']);
            $table->index(['tenant_id', 'category']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['tenant_id', 'language']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('document_templates');
    }
};