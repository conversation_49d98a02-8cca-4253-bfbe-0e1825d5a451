<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول المصروفات
     */
    public function up(): void
    {
        Schema::create('expenses', function (Blueprint $table) {
            $table->id();
            $table->foreignId('case_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('user_id')->constrained()->onDelete('restrict'); // من أنفق
            $table->string('title'); // عنوان المصروف
            $table->text('description')->nullable(); // وصف المصروف
            $table->decimal('amount', 10, 2); // المبلغ
            $table->string('currency', 3)->default('KWD'); // العملة
            $table->enum('category', [
                'court_fees', // رسوم محكمة
                'transportation', // مواصلات
                'communication', // اتصالات
                'documentation', // توثيق
                'expert_fees', // أتعاب خبراء
                'translation', // ترجمة
                'printing', // طباعة
                'postage', // بريد
                'meals', // وجبات
                'accommodation', // إقامة
                'office_supplies', // مستلزمات مكتبية
                'legal_research', // بحث قانوني
                'other' // أخرى
            ])->default('other');
            $table->date('expense_date'); // تاريخ المصروف
            $table->enum('payment_method', [
                'cash', // نقداً
                'credit_card', // بطاقة ائتمان
                'debit_card', // بطاقة خصم
                'bank_transfer', // تحويل بنكي
                'check', // شيك
                'petty_cash', // نثريات
                'other' // أخرى
            ])->default('cash');
            $table->string('receipt_number')->nullable(); // رقم الإيصال
            $table->string('vendor_name')->nullable(); // اسم المورد
            $table->string('receipt_file')->nullable(); // ملف الإيصال
            $table->boolean('is_billable')->default(true); // قابل للفوترة للعميل
            $table->boolean('is_reimbursable')->default(false); // قابل للاسترداد
            $table->boolean('is_approved')->default(false); // معتمد
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('approved_at')->nullable(); // تاريخ الاعتماد
            $table->text('approval_notes')->nullable(); // ملاحظات الاعتماد
            $table->boolean('is_invoiced')->default(false); // تم فوترته
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');
            $table->boolean('is_reimbursed')->default(false); // تم استرداده
            $table->datetime('reimbursed_at')->nullable(); // تاريخ الاسترداد
            $table->text('notes')->nullable(); // ملاحظات
            $table->json('tags')->nullable(); // علامات
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['case_id', 'expense_date']);
            $table->index(['user_id', 'expense_date']);
            $table->index(['tenant_id', 'category']);
            $table->index(['tenant_id', 'is_billable']);
            $table->index(['tenant_id', 'is_approved']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('expenses');
    }
};