# منصة سندان لأعمال المحاماة
## Sanadan Law Firm Management Platform

![منصة سندان](https://via.placeholder.com/800x400/1e40af/ffffff?text=منصة+سندان+لأعمال+المحاماة)

منصة سندان هي نظام إدارة شامل ومتطور لمكاتب المحاماة في دولة الكويت والمنطقة العربية. تم تصميم المنصة خصيصاً للبيئة القانونية العربية مع دعم كامل للغة العربية واتجاه RTL.

## 🌟 الميزات الرئيسية

### 📋 إدارة القضايا
- **تتبع شامل للقضايا**: إدارة كاملة لدورة حياة القضية من البداية حتى النهاية
- **أنواع قضايا متنوعة**: مدني، تجاري، جنائي، أسرة، إداري، عمالي، عقاري، مواريث
- **حالات القضايا**: تتبع مراحل القضية مع إمكانية التخصيص
- **الأولويات**: تصنيف القضايا حسب الأهمية والعجلة
- **ربط القضايا**: ربط القضايا ذات الصلة ببعضها البعض

### 👥 إدارة العملاء
- **ملفات عملاء شاملة**: معلومات كاملة للأفراد والشركات والجهات الحكومية
- **تاريخ التعاملات**: سجل كامل لجميع القضايا والمعاملات
- **جهات الاتصال الطارئة**: معلومات الاتصال في الحالات الطارئة
- **التصنيفات**: تصنيف العملاء حسب النوع والأهمية

### ⚖️ إدارة الجلسات والمحاكم
- **جدولة الجلسات**: تقويم تفاعلي لجدولة ومتابعة الجلسات
- **معلومات المحاكم**: قاعدة بيانات شاملة للمحاكم ودوائرها
- **أنواع الجلسات**: جلسات أولى، متابعة، ختامية، نطق بالحكم، استئناف
- **تتبع الحضور**: تسجيل حضور الأطراف والنتائج
- **التنبيهات**: تذكيرات تلقائية للجلسات القادمة

### 📄 إدارة المستندات المتطورة
- **تخزين سحابي آمن**: رفع وتخزين المستندات بأمان عالي
- **أنظمة تصنيف ذكية**: تصنيف تلقائي للمستندات حسب النوع والقضية
- **قوالب جاهزة**: مكتبة شاملة من القوالب القانونية
- **التوقيع الإلكتروني**: إمكانية التوقيع الرقمي على المستندات
- **OCR**: استخراج النصوص من الصور والمستندات الممسوحة ضوئياً
- **إدارة الإصدارات**: تتبع إصدارات المستندات والتغييرات

### 💰 النظام المالي المتكامل
- **إدارة الفواتير**: إنشاء وإرسال الفواتير بتصميم احترافي
- **تتبع المدفوعات**: متابعة حالة المدفوعات والمستحقات
- **بوابات الدفع**: تكامل مع K-Net، Stripe، PayPal
- **إدارة المصروفات**: تسجيل ومتابعة مصروفات القضايا
- **التقارير المالية**: تقارير مالية شاملة ومفصلة
- **الضرائب**: حساب الضرائب تلقائياً حسب القوانين الكويتية

### ⏰ إدارة الوقت والمهام
- **تسجيل الوقت**: تتبع دقيق للوقت المستغرق في كل قضية
- **إدارة المهام**: تخصيص وتتبع المهام للفريق
- **التقويم التفاعلي**: عرض شامل للمهام والجلسات والمواعيد
- **التذكيرات**: تنبيهات تلقائية للمهام والمواعيد المهمة
- **تقارير الإنتاجية**: تحليل الأداء والإنتاجية

### 🤖 الذكاء الاصطناعي المدمج
- **مساعد قانوني ذكي**: الإجابة على الاستفسارات القانونية
- **اقتراح القوانين**: اقتراح المواد القانونية ذات الصلة
- **تحليل القضايا**: تحليل القضايا المشابهة والسوابق
- **إنشاء المستندات**: صياغة تلقائية للعقود والمذكرات
- **التنبؤ بالنتائج**: تحليل احتمالية نجاح القضايا

### 🏢 نظام متعدد المستأجرين (Multi-Tenancy)
- **عزل كامل للبيانات**: كل مكتب محاماة له بيانات منفصلة ومحمية
- **نطاقات فرعية**: كل مكتب له نطاق فرعي خاص
- **إعدادات مخصصة**: تخصيص كامل لكل مكتب
- **أمان متقدم**: حماية عالية المستوى للبيانات

## 🛠️ التقنيات المستخدمة

### Backend
- **Laravel 11** - إطار العمل الرئيسي
- **PHP 8.3** - لغة البرمجة
- **MySQL 8.0** - قاعدة البيانات
- **Redis** - التخزين المؤقت والجلسات
- **Laravel Sanctum** - المصادقة والتفويض

### Frontend
- **Vue.js 3** - إطار العمل للواجهة الأمامية
- **TypeScript** - لغة البرمجة المطورة
- **Tailwind CSS** - إطار العمل للتصميم
- **Inertia.js** - ربط Frontend مع Backend
- **Pinia** - إدارة الحالة

### الحزم والمكتبات
- **Stancl/Tenancy** - نظام متعدد المستأجرين
- **Laravel Scout + Meilisearch** - البحث المتقدم
- **DomPDF** - إنشاء ملفات PDF
- **Spatie Packages** - الصلاحيات وتسجيل الأنشطة
- **AWS S3/Wasabi** - التخزين السحابي

### بوابات الدفع
- **K-Net** - الدفع الإلكتروني الكويتي
- **Stripe** - المدفوعات العالمية
- **PayPal** - المدفوعات الدولية

## 📦 متطلبات التشغيل

### متطلبات الخادم
- PHP 8.3 أو أحدث
- MySQL 8.0 أو أحدث
- Redis 7.0 أو أحدث
- Nginx/Apache
- SSL Certificate
- Composer
- Node.js 18+ و npm 8+

### متطلبات النظام
- ذاكرة: 4GB RAM كحد أدنى (8GB مُوصى)
- مساحة التخزين: 20GB كحد أدنى
- معالج: 2 cores كحد أدنى

## 🚀 التثبيت والإعداد

### 1. استنساخ المشروع
```bash
git clone https://github.com/your-repo/sanadan-platform.git
cd sanadan-platform
```

### 2. تثبيت التبعيات
```bash
# تثبيت تبعيات PHP
composer install

# تثبيت تبعيات Node.js
npm install
```

### 3. إعداد البيئة
```bash
# نسخ ملف البيئة
cp .env.example .env

# إنشاء مفتاح التطبيق
php artisan key:generate
```

### 4. إعداد قاعدة البيانات
```bash
# تشغيل الهجرات
php artisan migrate

# تشغيل البذور
php artisan db:seed
```

### 5. إعداد التخزين
```bash
# ربط التخزين
php artisan storage:link

# إعداد الصلاحيات
chmod -R 775 storage bootstrap/cache
```

### 6. بناء الأصول
```bash
# للتطوير
npm run dev

# للإنتاج
npm run build
```

### 7. تشغيل الخادم
```bash
# خادم التطوير
php artisan serve

# أو استخدام Laravel Sail
./vendor/bin/sail up
```

## ⚙️ الإعدادات

### إعداد البريد الإلكتروني
```env
MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=your-email
MAIL_PASSWORD=your-password
MAIL_ENCRYPTION=tls
```

### إعداد التخزين السحابي
```env
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-bucket-name
```

### إعداد بوابات الدفع
```env
# Stripe
STRIPE_KEY=pk_test_...
STRIPE_SECRET=sk_test_...

# K-Net
KNET_MERCHANT_ID=your-merchant-id
KNET_PASSWORD=your-password
KNET_TERMINAL_ID=your-terminal-id

# PayPal
PAYPAL_CLIENT_ID=your-client-id
PAYPAL_CLIENT_SECRET=your-client-secret
PAYPAL_MODE=sandbox
```

## 📱 الاستخدام

### إنشاء مستأجر جديد
```bash
php artisan tenant:create example.sanadan.com "مكتب المحاماة المثالي"
```

### إنشاء مستخدم مدير
```bash
php artisan make:admin <EMAIL> "اسم المدير"
```

### تشغيل المهام المجدولة
```bash
# إضافة إلى crontab
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

### تشغيل الطوابير
```bash
php artisan queue:work
```

## 🔒 الأمان

### الميزات الأمنية
- تشفير كامل للبيانات الحساسة
- مصادقة ثنائية العوامل (2FA)
- تسجيل شامل للأنشطة
- حماية من CSRF و XSS
- تحديثات أمنية تلقائية
- نسخ احتياطية مشفرة

### أفضل الممارسات
- استخدام HTTPS دائماً
- تحديث كلمات المرور بانتظام
- مراجعة سجلات الأنشطة
- تحديث النظام بانتظام

## 📊 التقارير والإحصائيات

### التقارير المتاحة
- تقارير مالية شهرية/سنوية
- تقارير أداء المحامين
- إحصائيات القضايا
- تقارير الوقت المستغرق
- تحليل الإيرادات والمصروفات

### تصدير البيانات
- Excel (.xlsx)
- PDF
- CSV
- JSON

## 🌐 دعم اللغات

### اللغات المدعومة
- العربية (الأساسية) - RTL
- الإنجليزية - LTR
- الفرنسية (اختيارية) - LTR

### إضافة لغة جديدة
```bash
php artisan lang:add fr
```

## 🔧 التخصيص

### تخصيص الألوان والتصميم
يمكن تخصيص ألوان المنصة من خلال ملف `tailwind.config.js`:

```javascript
colors: {
  primary: {
    600: '#your-primary-color',
  },
  // المزيد من الألوان...
}
```

### إضافة قوالب مستندات جديدة
```bash
php artisan make:template "اسم القالب" --type=contract
```

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
# جميع الاختبارات
php artisan test

# اختبارات محددة
php artisan test --filter=CaseTest

# اختبارات مع التغطية
php artisan test --coverage
```

### أنواع الاختبارات
- Unit Tests
- Feature Tests
- Browser Tests
- API Tests

## 📈 الأداء والتحسين

### تحسين الأداء
```bash
# تحسين التطبيق
php artisan optimize

# تحسين قاعدة البيانات
php artisan db:optimize

# تحسين الصور
php artisan images:optimize
```

### المراقبة
- New Relic للمراقبة
- Laravel Telescope للتطوير
- Laravel Horizon للطوابير

## 🤝 المساهمة

نرحب بمساهماتكم في تطوير منصة سندان! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/amazing-feature`)
3. Commit التغييرات (`git commit -m 'Add amazing feature'`)
4. Push للفرع (`git push origin feature/amazing-feature`)
5. فتح Pull Request

### معايير الكود
- اتباع PSR-12 لـ PHP
- استخدام ESLint لـ JavaScript/TypeScript
- كتابة اختبارات للميزات الجديدة
- توثيق الكود باللغة العربية

## 📞 الدعم والمساعدة

### طرق التواصل
- البريد الإلكتروني: <EMAIL>
- الهاتف: +965 2XXX XXXX
- الموقع: https://sanadan.com
- التوثيق: https://docs.sanadan.com

### ساعات الدعم
- الأحد - الخميس: 8:00 ص - 6:00 م (توقيت الكويت)
- الجمعة - السبت: 10:00 ص - 4:00 م (توقيت الكويت)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف [LICENSE](LICENSE) للمزيد من التفاصيل.

## 🙏 شكر وتقدير

نشكر جميع المساهمين والمطورين الذين ساهموا في تطوير هذه المنصة:

- فريق Laravel لإطار العمل الرائع
- فريق Vue.js للواجهة الأمامية المتطورة
- فريق Tailwind CSS للتصميم الجميل
- المجتمع العربي للمطورين للدعم والمساندة

---

**منصة سندان - حيث تلتقي التكنولوجيا بالعدالة** ⚖️

Made with ❤️ in Kuwait 🇰🇼