<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول حالات القضايا
     */
    public function up(): void
    {
        Schema::create('case_statuses', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->text('description')->nullable();
            $table->string('color', 7)->default('#6B7280'); // لون للتمييز
            $table->string('icon')->nullable(); // أيقونة
            $table->integer('order')->default(0); // ترتيب الحالة
            $table->boolean('is_final')->default(false); // هل هي حالة نهائية
            $table->boolean('is_active')->default(true);
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['tenant_id', 'is_active']);
            $table->index(['tenant_id', 'order']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('case_statuses');
    }
};