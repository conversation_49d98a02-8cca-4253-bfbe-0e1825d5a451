<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول المحاكم
     */
    public function up(): void
    {
        Schema::create('courts', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_en')->nullable();
            $table->enum('type', [
                'supreme', // المحكمة العليا
                'appeal', // محكمة الاستئناف
                'first_instance', // محكمة أول درجة
                'administrative', // المحكمة الإدارية
                'constitutional', // المحكمة الدستورية
                'commercial', // المحكمة التجارية
                'family', // محكمة الأسرة
                'criminal', // المحكمة الجنائية
                'civil' // المحكمة المدنية
            ]);
            $table->text('address');
            $table->string('phone')->nullable();
            $table->string('fax')->nullable();
            $table->string('email')->nullable();
            $table->string('website')->nullable();
            $table->json('working_hours')->nullable(); // ساعات العمل
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['tenant_id', 'type']);
            $table->index(['tenant_id', 'is_active']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('courts');
    }
};