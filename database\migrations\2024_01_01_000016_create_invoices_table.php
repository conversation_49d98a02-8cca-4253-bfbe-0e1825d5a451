<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول الفواتير
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique(); // رقم الفاتورة
            $table->foreignId('client_id')->constrained()->onDelete('restrict');
            $table->foreignId('case_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('created_by')->constrained('users')->onDelete('restrict');
            $table->enum('type', [
                'service', // خدمة
                'consultation', // استشارة
                'retainer', // أتعاب مقدمة
                'court_fees', // رسوم محكمة
                'expenses', // مصاريف
                'other' // أخرى
            ])->default('service');
            $table->date('issue_date'); // تاريخ الإصدار
            $table->date('due_date'); // تاريخ الاستحقاق
            $table->decimal('subtotal', 12, 2); // المبلغ الفرعي
            $table->decimal('tax_percentage', 5, 2)->default(0); // نسبة الضريبة
            $table->decimal('tax_amount', 12, 2)->default(0); // مبلغ الضريبة
            $table->decimal('discount_percentage', 5, 2)->default(0); // نسبة الخصم
            $table->decimal('discount_amount', 12, 2)->default(0); // مبلغ الخصم
            $table->decimal('total_amount', 12, 2); // المبلغ الإجمالي
            $table->decimal('paid_amount', 12, 2)->default(0); // المبلغ المدفوع
            $table->decimal('balance', 12, 2)->default(0); // الرصيد المتبقي
            $table->enum('status', [
                'draft', // مسودة
                'sent', // مرسلة
                'viewed', // تم عرضها
                'paid', // مدفوعة
                'partially_paid', // مدفوعة جزئياً
                'overdue', // متأخرة
                'cancelled', // ملغية
                'refunded' // مسترد
            ])->default('draft');
            $table->string('currency', 3)->default('KWD'); // العملة
            $table->text('notes')->nullable(); // ملاحظات
            $table->text('terms_conditions')->nullable(); // الشروط والأحكام
            $table->json('line_items')->nullable(); // بنود الفاتورة
            $table->json('payment_methods')->nullable(); // طرق الدفع المقبولة
            $table->string('payment_link')->nullable(); // رابط الدفع
            $table->datetime('sent_at')->nullable(); // تاريخ الإرسال
            $table->datetime('viewed_at')->nullable(); // تاريخ العرض
            $table->datetime('paid_at')->nullable(); // تاريخ الدفع
            $table->integer('reminder_count')->default(0); // عدد التذكيرات
            $table->datetime('last_reminder_at')->nullable(); // آخر تذكير
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['client_id', 'status']);
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'due_date']);
            $table->index(['case_id', 'status']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};