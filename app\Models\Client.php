<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\Scout\Searchable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

/**
 * نموذج العميل - إدارة العملاء
 */
class Client extends Model
{
    use HasFactory, SoftDeletes, Searchable, LogsActivity;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'name',
        'email',
        'phone',
        'phone_secondary',
        'address',
        'id_number',
        'passport_number',
        'client_type',
        'company_name',
        'commercial_registration',
        'birth_date',
        'gender',
        'nationality',
        'occupation',
        'notes',
        'emergency_contacts',
        'status',
        'tenant_id',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'birth_date' => 'date',
        'emergency_contacts' => 'array',
    ];

    /**
     * إعدادات تسجيل الأنشطة
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'phone', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * إعدادات البحث
     */
    public function toSearchableArray(): array
    {
        return [
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'id_number' => $this->id_number,
            'company_name' => $this->company_name,
        ];
    }

    /**
     * العلاقة مع القضايا
     */
    public function cases()
    {
        return $this->hasMany(CaseModel::class, 'client_id');
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks()
    {
        return $this->hasMany(Task::class, 'client_id');
    }

    /**
     * العلاقة مع المستندات
     */
    public function documents()
    {
        return $this->hasMany(Document::class, 'client_id');
    }

    /**
     * العلاقة مع الفواتير
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'client_id');
    }

    /**
     * العلاقة مع المدفوعات
     */
    public function payments()
    {
        return $this->hasMany(Payment::class, 'client_id');
    }

    /**
     * العلاقة مع المصروفات
     */
    public function expenses()
    {
        return $this->hasMany(Expense::class, 'client_id');
    }

    /**
     * الحصول على القضايا النشطة
     */
    public function activeCases()
    {
        return $this->cases()->active();
    }

    /**
     * الحصول على القضايا المكتملة
     */
    public function completedCases()
    {
        return $this->cases()->completed();
    }

    /**
     * الحصول على الفواتير المعلقة
     */
    public function pendingInvoices()
    {
        return $this->invoices()->whereIn('status', ['sent', 'viewed', 'overdue']);
    }

    /**
     * الحصول على إجمالي المبلغ المستحق
     */
    public function getTotalOutstandingAttribute(): float
    {
        return $this->invoices()
            ->whereIn('status', ['sent', 'viewed', 'overdue', 'partially_paid'])
            ->sum('balance');
    }

    /**
     * الحصول على إجمالي المدفوعات
     */
    public function getTotalPaidAttribute(): float
    {
        return $this->payments()
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * الحصول على عدد القضايا النشطة
     */
    public function getActiveCasesCountAttribute(): int
    {
        return $this->activeCases()->count();
    }

    /**
     * الحصول على عدد القضايا المكتملة
     */
    public function getCompletedCasesCountAttribute(): int
    {
        return $this->completedCases()->count();
    }

    /**
     * التحقق من كون العميل شركة
     */
    public function isCompany(): bool
    {
        return $this->client_type === 'company';
    }

    /**
     * التحقق من كون العميل فرد
     */
    public function isIndividual(): bool
    {
        return $this->client_type === 'individual';
    }

    /**
     * التحقق من كون العميل جهة حكومية
     */
    public function isGovernment(): bool
    {
        return $this->client_type === 'government';
    }

    /**
     * التحقق من حالة العميل النشطة
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * التحقق من كون العميل في القائمة السوداء
     */
    public function isBlacklisted(): bool
    {
        return $this->status === 'blacklisted';
    }

    /**
     * الحصول على الاسم المناسب للعرض
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->isCompany()) {
            return $this->company_name ?: $this->name;
        }
        
        return $this->name;
    }

    /**
     * الحصول على معلومات الاتصال الأساسية
     */
    public function getPrimaryContactAttribute(): array
    {
        return [
            'phone' => $this->phone,
            'email' => $this->email,
        ];
    }

    /**
     * الحصول على العمر (للأفراد)
     */
    public function getAgeAttribute(): ?int
    {
        if (!$this->birth_date || !$this->isIndividual()) {
            return null;
        }
        
        return $this->birth_date->age;
    }

    /**
     * Scope للعملاء النشطين
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope للعملاء الأفراد
     */
    public function scopeIndividuals($query)
    {
        return $query->where('client_type', 'individual');
    }

    /**
     * Scope للعملاء الشركات
     */
    public function scopeCompanies($query)
    {
        return $query->where('client_type', 'company');
    }

    /**
     * Scope للجهات الحكومية
     */
    public function scopeGovernment($query)
    {
        return $query->where('client_type', 'government');
    }

    /**
     * Scope للبحث بالاسم أو البريد الإلكتروني أو الهاتف
     */
    public function scopeSearch($query, $term)
    {
        return $query->where(function ($q) use ($term) {
            $q->where('name', 'like', "%{$term}%")
              ->orWhere('email', 'like', "%{$term}%")
              ->orWhere('phone', 'like', "%{$term}%")
              ->orWhere('id_number', 'like', "%{$term}%")
              ->orWhere('company_name', 'like', "%{$term}%");
        });
    }
}