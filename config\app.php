<?php

use Illuminate\Support\Facades\Facade;
use Illuminate\Support\ServiceProvider;

return [

    /*
    |--------------------------------------------------------------------------
    | اسم التطبيق
    |--------------------------------------------------------------------------
    |
    | هذه القيمة هي اسم تطبيقك. يتم استخدام هذه القيمة عندما يحتاج
    | الإطار إلى وضع اسم التطبيق في إشعار أو أي مكان آخر حسب الحاجة.
    |
    */

    'name' => env('APP_NAME', 'منصة سندان لأعمال المحاماة'),

    /*
    |--------------------------------------------------------------------------
    | بيئة التطبيق
    |--------------------------------------------------------------------------
    |
    | هذه القيمة تحدد "البيئة" التي يعمل فيها تطبيقك حالياً.
    | قد يحدد هذا كيفية تفضيلك لتكوين خدمات مختلفة يستخدمها تطبيقك.
    |
    */

    'env' => env('APP_ENV', 'production'),

    /*
    |--------------------------------------------------------------------------
    | وضع التطوير
    |--------------------------------------------------------------------------
    |
    | عندما يكون تطبيقك في وضع التطوير، ستظهر رسائل خطأ مفصلة مع
    | تتبع المكدس في كل خطأ يحدث داخل تطبيقك. إذا تم تعطيله،
    | ستظهر صفحة خطأ عامة بسيطة.
    |
    */

    'debug' => (bool) env('APP_DEBUG', false),

    /*
    |--------------------------------------------------------------------------
    | رابط التطبيق
    |--------------------------------------------------------------------------
    |
    | يتم استخدام هذا الرابط من قبل وحدة التحكم لإنشاء روابط صحيحة
    | عند استخدام أداة سطر الأوامر Artisan. يجب عليك تعيين هذا إلى
    | جذر تطبيقك بحيث يتم استخدامه عند تشغيل مهام Artisan.
    |
    */

    'url' => env('APP_URL', 'http://localhost'),

    'asset_url' => env('ASSET_URL'),

    /*
    |--------------------------------------------------------------------------
    | المنطقة الزمنية للتطبيق
    |--------------------------------------------------------------------------
    |
    | هنا يمكنك تحديد المنطقة الزمنية الافتراضية لتطبيقك، والتي
    | ستستخدم بواسطة دوال التاريخ والوقت في PHP. لقد قمنا بتعيين
    | هذا إلى افتراضي معقول لك خارج الصندوق.
    |
    */

    'timezone' => env('APP_TIMEZONE', 'Asia/Kuwait'),

    /*
    |--------------------------------------------------------------------------
    | تكوين اللغة المحلية
    |--------------------------------------------------------------------------
    |
    | اللغة المحلية الافتراضية التي ستستخدم بواسطة مزود خدمة الترجمة.
    | أنت حر في تعيين هذه القيمة إلى أي من اللغات المحلية التي ستكون
    | مدعومة من قبل تطبيقك.
    |
    */

    'locale' => env('APP_LOCALE', 'ar'),

    /*
    |--------------------------------------------------------------------------
    | اللغة المحلية الاحتياطية
    |--------------------------------------------------------------------------
    |
    | اللغة المحلية الاحتياطية تحدد اللغة المحلية التي ستستخدم عندما
    | لا تكون اللغة المحلية الحالية متاحة. يمكنك تغيير القيمة إلى أي من
    | مجلدات اللغة المدعومة من قبل تطبيقك.
    |
    */

    'fallback_locale' => env('APP_FALLBACK_LOCALE', 'en'),

    /*
    |--------------------------------------------------------------------------
    | لغة محلية وهمية
    |--------------------------------------------------------------------------
    |
    | هذه اللغة المحلية ستستخدم من قبل مزود خدمة الترجمة الوهمي في PHP
    | لإنشاء بيانات وهمية محلية لتطبيقك. هذا مفيد لملء قاعدة البيانات
    | الخاصة بك ببيانات وهمية عبر Laravel's model factory system.
    |
    */

    'faker_locale' => env('APP_FAKER_LOCALE', 'ar_SA'),

    /*
    |--------------------------------------------------------------------------
    | مفتاح التشفير
    |--------------------------------------------------------------------------
    |
    | يستخدم هذا المفتاح بواسطة خدمة تشفير Illuminate ويجب أن يتم تعيينه
    | إلى سلسلة عشوائية 32 حرف، وإلا فإن هذه السلاسل المشفرة لن تكون آمنة.
    | يرجى القيام بذلك قبل نشر التطبيق!
    |
    */

    'key' => env('APP_KEY'),

    'cipher' => 'AES-256-CBC',

    /*
    |--------------------------------------------------------------------------
    | مزودي الخدمة المحملين تلقائياً
    |--------------------------------------------------------------------------
    |
    | مزودو الخدمة المدرجون هنا سيتم تحميلهم تلقائياً في الطلب
    | إلى تطبيقك. لا تتردد في إضافة خدماتك الخاصة إلى هذا المصفوف
    | لمنح وظائف موسعة لتطبيقاتك.
    |
    */

    'providers' => ServiceProvider::defaultProviders()->merge([
        /*
         * مزودي خدمة الحزم...
         */
        Stancl\Tenancy\TenancyServiceProvider::class,
        Laravel\Sanctum\SanctumServiceProvider::class,
        Spatie\Permission\PermissionServiceProvider::class,
        Spatie\Activitylog\ActivitylogServiceProvider::class,
        Barryvdh\DomPDF\ServiceProvider::class,

        /*
         * مزودي خدمة التطبيق...
         */
        App\Providers\AppServiceProvider::class,
        App\Providers\AuthServiceProvider::class,
        // App\Providers\BroadcastServiceProvider::class,
        App\Providers\EventServiceProvider::class,
        App\Providers\RouteServiceProvider::class,
        App\Providers\TenancyServiceProvider::class,
    ])->toArray(),

    /*
     |--------------------------------------------------------------------------
     | أسماء مستعارة للفئات
     |--------------------------------------------------------------------------
     |
     | هذا المصفوف من أسماء الفئات المستعارة سيتم تسجيله عندما يتم تشغيل
     | هذا التطبيق. ومع ذلك، لا تتردد في تسجيل أكبر عدد تريده حيث أن
     | الأسماء المستعارة يتم "تحميلها بكسل" لذا فهي لا تعيق الأداء.
     |
     */

    'aliases' => Facade::defaultAliases()->merge([
        'PDF' => Barryvdh\DomPDF\Facade\Pdf::class,
    ])->toArray(),

];