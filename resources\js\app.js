import './bootstrap';
import '../css/app.css';

import { createApp, h } from 'vue';
import { createInertiaApp } from '@inertiajs/vue3';
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers';
import { ZiggyVue } from '../../vendor/tightenco/ziggy/dist/vue.m';
import { createPinia } from 'pinia';
import { createI18n } from 'vue-i18n';

// استيراد الترجمات
import arMessages from '../lang/ar/app.json';
import enMessages from '../lang/en/app.json';

// إعداد نظام الترجمة
const i18n = createI18n({
    legacy: false,
    locale: document.documentElement.lang || 'ar',
    fallbackLocale: 'en',
    messages: {
        ar: arMessages,
        en: enMessages,
    },
});

// إعداد Pinia لإدارة الحالة
const pinia = createPinia();

const appName = import.meta.env.VITE_APP_NAME || 'منصة سندان لأعمال المحاماة';

createInertiaApp({
    title: (title) => title ? `${title} - ${appName}` : appName,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.vue`, import.meta.glob('./Pages/**/*.vue')),
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .use(pinia)
            .use(i18n);

        // إعداد الخصائص العامة
        app.config.globalProperties.$appName = appName;
        
        // إعداد اتجاه الصفحة حسب اللغة
        const locale = props.initialPage.props.locale || 'ar';
        document.documentElement.lang = locale;
        document.documentElement.dir = locale === 'ar' ? 'rtl' : 'ltr';
        
        // إعداد الوضع الليلي
        const isDark = localStorage.getItem('darkMode') === 'true';
        if (isDark) {
            document.documentElement.classList.add('dark');
        }

        // تسجيل المكونات العامة
        app.component('AppLayout', () => import('./Layouts/AppLayout.vue'));
        app.component('GuestLayout', () => import('./Layouts/GuestLayout.vue'));
        app.component('AuthenticatedLayout', () => import('./Layouts/AuthenticatedLayout.vue'));

        return app.mount(el);
    },
    progress: {
        color: '#1e40af',
        showSpinner: true,
    },
});

// إعداد معالج الأخطاء العام
window.addEventListener('unhandledrejection', event => {
    console.error('Unhandled promise rejection:', event.reason);
});

// إعداد Axios للطلبات
window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';
window.axios.defaults.headers.common['Accept'] = 'application/json';

// إعداد CSRF Token
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    window.axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
}

// إعداد Interceptors للاستجابة
window.axios.interceptors.response.use(
    response => response,
    error => {
        if (error.response?.status === 401) {
            // إعادة توجيه لصفحة تسجيل الدخول
            window.location.href = '/login';
        } else if (error.response?.status === 403) {
            // عرض رسالة عدم وجود صلاحية
            console.error('Access denied');
        } else if (error.response?.status >= 500) {
            // عرض رسالة خطأ في الخادم
            console.error('Server error');
        }
        return Promise.reject(error);
    }
);

// دوال مساعدة عامة
window.formatCurrency = (amount, currency = 'KWD') => {
    return new Intl.NumberFormat('ar-KW', {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2,
    }).format(amount);
};

window.formatDate = (date, locale = 'ar') => {
    return new Intl.DateTimeFormat(locale === 'ar' ? 'ar-KW' : 'en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
    }).format(new Date(date));
};

window.formatDateTime = (date, locale = 'ar') => {
    return new Intl.DateTimeFormat(locale === 'ar' ? 'ar-KW' : 'en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    }).format(new Date(date));
};

window.formatNumber = (number, locale = 'ar') => {
    return new Intl.NumberFormat(locale === 'ar' ? 'ar-KW' : 'en-US').format(number);
};

// دالة لتبديل الوضع الليلي
window.toggleDarkMode = () => {
    const isDark = document.documentElement.classList.contains('dark');
    if (isDark) {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('darkMode', 'false');
    } else {
        document.documentElement.classList.add('dark');
        localStorage.setItem('darkMode', 'true');
    }
};

// دالة لتبديل اللغة
window.toggleLanguage = () => {
    const currentLang = document.documentElement.lang;
    const newLang = currentLang === 'ar' ? 'en' : 'ar';
    
    // تحديث اتجاه الصفحة
    document.documentElement.lang = newLang;
    document.documentElement.dir = newLang === 'ar' ? 'rtl' : 'ltr';
    
    // حفظ اللغة في التخزين المحلي
    localStorage.setItem('locale', newLang);
    
    // إعادة تحميل الصفحة لتطبيق التغييرات
    window.location.reload();
};

// دالة لنسخ النص إلى الحافظة
window.copyToClipboard = async (text) => {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (err) {
        console.error('Failed to copy text: ', err);
        return false;
    }
};

// دالة لتحميل الملفات
window.downloadFile = (url, filename) => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

// دالة للتحقق من صحة البريد الإلكتروني
window.isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
};

// دالة للتحقق من صحة رقم الهاتف الكويتي
window.isValidKuwaitPhone = (phone) => {
    const phoneRegex = /^(\+965|965)?[2569]\d{7}$/;
    return phoneRegex.test(phone.replace(/\s/g, ''));
};

// دالة لتنسيق رقم الهاتف الكويتي
window.formatKuwaitPhone = (phone) => {
    const cleaned = phone.replace(/\D/g, '');
    if (cleaned.startsWith('965')) {
        return `+965 ${cleaned.slice(3, 7)} ${cleaned.slice(7)}`;
    } else if (cleaned.length === 8) {
        return `+965 ${cleaned.slice(0, 4)} ${cleaned.slice(4)}`;
    }
    return phone;
};

// دالة لحساب الفرق بين التواريخ
window.dateDiff = (date1, date2, unit = 'days') => {
    const diff = new Date(date2) - new Date(date1);
    const msPerUnit = {
        days: 24 * 60 * 60 * 1000,
        hours: 60 * 60 * 1000,
        minutes: 60 * 1000,
        seconds: 1000,
    };
    return Math.floor(diff / msPerUnit[unit]);
};

// دالة لتحويل الملف إلى Base64
window.fileToBase64 = (file) => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = error => reject(error);
    });
};

// دالة لضغط الصور
window.compressImage = (file, quality = 0.8, maxWidth = 1920, maxHeight = 1080) => {
    return new Promise((resolve) => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        const img = new Image();
        
        img.onload = () => {
            const { width, height } = img;
            const ratio = Math.min(maxWidth / width, maxHeight / height);
            
            canvas.width = width * ratio;
            canvas.height = height * ratio;
            
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
            
            canvas.toBlob(resolve, 'image/jpeg', quality);
        };
        
        img.src = URL.createObjectURL(file);
    });
};

// إعداد Service Worker للتخزين المؤقت
if ('serviceWorker' in navigator) {
    window.addEventListener('load', () => {
        navigator.serviceWorker.register('/sw.js')
            .then((registration) => {
                console.log('SW registered: ', registration);
            })
            .catch((registrationError) => {
                console.log('SW registration failed: ', registrationError);
            });
    });
}