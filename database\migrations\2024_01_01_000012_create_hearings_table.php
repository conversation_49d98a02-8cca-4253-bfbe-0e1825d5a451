<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول الجلسات
     */
    public function up(): void
    {
        Schema::create('hearings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('case_id')->constrained()->onDelete('cascade');
            $table->foreignId('court_chamber_id')->constrained()->onDelete('restrict');
            $table->date('hearing_date'); // تاريخ الجلسة
            $table->time('hearing_time'); // وقت الجلسة
            $table->enum('type', [
                'first', // جلسة أولى
                'continuation', // جلسة متابعة
                'final', // جلسة ختامية
                'verdict', // جلسة نطق بالحكم
                'appeal', // جلسة استئناف
                'execution', // جلسة تنفيذ
                'mediation', // جلسة وساطة
                'expert', // جلسة خبرة
                'witness' // جلسة شهود
            ])->default('continuation');
            $table->enum('status', [
                'scheduled', // مجدولة
                'in_progress', // جارية
                'completed', // مكتملة
                'postponed', // مؤجلة
                'cancelled', // ملغية
                'no_show' // عدم حضور
            ])->default('scheduled');
            $table->text('agenda')->nullable(); // جدول أعمال الجلسة
            $table->text('notes')->nullable(); // ملاحظات الجلسة
            $table->text('outcome')->nullable(); // نتيجة الجلسة
            $table->text('judge_decision')->nullable(); // قرار القاضي
            $table->date('next_hearing_date')->nullable(); // تاريخ الجلسة القادمة
            $table->time('next_hearing_time')->nullable(); // وقت الجلسة القادمة
            $table->json('attendees')->nullable(); // الحاضرون
            $table->json('documents_presented')->nullable(); // المستندات المقدمة
            $table->boolean('client_attended')->default(false); // حضور العميل
            $table->boolean('lawyer_attended')->default(false); // حضور المحامي
            $table->boolean('opponent_attended')->default(false); // حضور الخصم
            $table->text('postponement_reason')->nullable(); // سبب التأجيل
            $table->integer('duration_minutes')->nullable(); // مدة الجلسة بالدقائق
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['case_id', 'hearing_date']);
            $table->index(['tenant_id', 'hearing_date']);
            $table->index(['tenant_id', 'status']);
            $table->index(['court_chamber_id', 'hearing_date']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('hearings');
    }
};