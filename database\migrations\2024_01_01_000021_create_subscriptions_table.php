<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول الاشتراكات
     */
    public function up(): void
    {
        Schema::create('subscriptions', function (Blueprint $table) {
            $table->id();
            $table->string('tenant_id');
            $table->foreignId('subscription_plan_id')->constrained()->onDelete('restrict');
            $table->string('stripe_subscription_id')->nullable(); // معرف الاشتراك في Stripe
            $table->string('paypal_subscription_id')->nullable(); // معرف الاشتراك في PayPal
            $table->enum('status', [
                'trial', // فترة تجريبية
                'active', // نشط
                'past_due', // متأخر الدفع
                'cancelled', // ملغي
                'unpaid', // غير مدفوع
                'incomplete', // غير مكتمل
                'incomplete_expired', // منتهي الصلاحية غير مكتمل
                'suspended', // معلق
                'expired' // منتهي الصلاحية
            ])->default('trial');
            $table->datetime('trial_starts_at')->nullable(); // بداية الفترة التجريبية
            $table->datetime('trial_ends_at')->nullable(); // نهاية الفترة التجريبية
            $table->datetime('starts_at'); // بداية الاشتراك
            $table->datetime('ends_at')->nullable(); // نهاية الاشتراك
            $table->datetime('cancelled_at')->nullable(); // تاريخ الإلغاء
            $table->text('cancellation_reason')->nullable(); // سبب الإلغاء
            $table->decimal('amount', 8, 2); // مبلغ الاشتراك
            $table->string('currency', 3)->default('KWD'); // العملة
            $table->enum('billing_cycle', [
                'monthly', // شهري
                'quarterly', // ربع سنوي
                'semi_annual', // نصف سنوي
                'annual', // سنوي
                'lifetime' // مدى الحياة
            ])->default('monthly');
            $table->datetime('next_billing_date')->nullable(); // تاريخ الفوترة القادم
            $table->datetime('last_payment_date')->nullable(); // تاريخ آخر دفعة
            $table->decimal('last_payment_amount', 8, 2)->nullable(); // مبلغ آخر دفعة
            $table->integer('failed_payment_attempts')->default(0); // محاولات الدفع الفاشلة
            $table->json('usage_limits')->nullable(); // حدود الاستخدام الحالية
            $table->json('usage_current')->nullable(); // الاستخدام الحالي
            $table->boolean('auto_renew')->default(true); // التجديد التلقائي
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->text('notes')->nullable(); // ملاحظات
            $table->timestamps();

            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');
            $table->index(['tenant_id', 'status']);
            $table->index(['status', 'ends_at']);
            $table->index('next_billing_date');
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('subscriptions');
    }
};