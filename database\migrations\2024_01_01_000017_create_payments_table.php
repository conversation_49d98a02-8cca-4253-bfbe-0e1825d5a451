<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول المدفوعات
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('restrict');
            $table->foreignId('client_id')->constrained()->onDelete('restrict');
            $table->string('payment_number')->unique(); // رقم الدفعة
            $table->decimal('amount', 12, 2); // المبلغ
            $table->string('currency', 3)->default('KWD'); // العملة
            $table->enum('payment_method', [
                'cash', // نقداً
                'bank_transfer', // تحويل بنكي
                'check', // شيك
                'credit_card', // بطاقة ائتمان
                'debit_card', // بطاقة خصم
                'knet', // كي نت
                'online', // دفع إلكتروني
                'paypal', // باي بال
                'stripe', // سترايب
                'other' // أخرى
            ]);
            $table->date('payment_date'); // تاريخ الدفع
            $table->string('reference_number')->nullable(); // رقم المرجع
            $table->string('transaction_id')->nullable(); // معرف المعاملة
            $table->string('gateway_response')->nullable(); // رد البوابة
            $table->enum('status', [
                'pending', // معلق
                'processing', // قيد المعالجة
                'completed', // مكتمل
                'failed', // فشل
                'cancelled', // ملغي
                'refunded', // مسترد
                'disputed' // متنازع عليه
            ])->default('pending');
            $table->text('notes')->nullable(); // ملاحظات
            $table->json('gateway_data')->nullable(); // بيانات البوابة
            $table->decimal('fees', 8, 2)->default(0); // رسوم المعاملة
            $table->string('receipt_number')->nullable(); // رقم الإيصال
            $table->string('bank_name')->nullable(); // اسم البنك
            $table->string('check_number')->nullable(); // رقم الشيك
            $table->date('check_date')->nullable(); // تاريخ الشيك
            $table->foreignId('processed_by')->nullable()->constrained('users')->onDelete('set null'); // معالج الدفعة
            $table->datetime('processed_at')->nullable(); // تاريخ المعالجة
            $table->boolean('is_refunded')->default(false); // مسترد
            $table->decimal('refunded_amount', 12, 2)->default(0); // المبلغ المسترد
            $table->datetime('refunded_at')->nullable(); // تاريخ الاسترداد
            $table->text('refund_reason')->nullable(); // سبب الاسترداد
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['invoice_id', 'status']);
            $table->index(['client_id', 'payment_date']);
            $table->index(['tenant_id', 'payment_method']);
            $table->index(['tenant_id', 'status']);
            $table->index(['tenant_id', 'payment_date']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};