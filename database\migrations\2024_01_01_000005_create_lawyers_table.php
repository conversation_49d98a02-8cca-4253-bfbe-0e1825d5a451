<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول المحامين
     */
    public function up(): void
    {
        Schema::create('lawyers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('law_firm_id')->constrained()->onDelete('cascade');
            $table->string('license_number')->unique();
            $table->string('bar_association')->nullable(); // نقابة المحامين
            $table->date('license_date')->nullable();
            $table->date('license_expiry')->nullable();
            $table->json('specializations')->nullable(); // التخصصات القانونية
            $table->integer('experience_years')->default(0);
            $table->text('bio')->nullable();
            $table->json('qualifications')->nullable(); // المؤهلات والشهادات
            $table->json('languages')->nullable(); // اللغات المتحدث بها
            $table->decimal('hourly_rate', 8, 2)->nullable(); // السعر بالساعة
            $table->enum('status', ['active', 'inactive', 'suspended'])->default('active');
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['tenant_id', 'status']);
            $table->index(['law_firm_id', 'status']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('lawyers');
    }
};