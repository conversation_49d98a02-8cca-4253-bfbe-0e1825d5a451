@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* استيراد خطوط جوجل العربية */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

/* إعدادات أساسية للغة العربية */
:root {
  --font-arabic: 'Cairo', 'Amiri', 'Noto Sans Arabic', sans-serif;
  --font-english: 'Inter', 'Roboto', sans-serif;
  
  /* ألوان منصة سندان */
  --color-primary: #1e40af;
  --color-secondary: #dc2626;
  --color-success: #16a34a;
  --color-warning: #d97706;
  --color-info: #0891b2;
  --color-dark: #1f2937;
  --color-light: #f9fafb;
  --color-gold: #d4af37;
  
  /* الظلال */
  --shadow-soft: 0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04);
  --shadow-medium: 0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-strong: 0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 4px 25px -5px rgba(0, 0, 0, 0.1);
}

/* إعدادات الوضع الليلي */
.dark {
  --color-bg-primary: #1f2937;
  --color-bg-secondary: #374151;
  --color-text-primary: #f9fafb;
  --color-text-secondary: #d1d5db;
}

/* إعدادات أساسية للصفحة */
html {
  direction: rtl;
  scroll-behavior: smooth;
}

html[dir="ltr"] {
  direction: ltr;
}

body {
  font-family: var(--font-arabic);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body[dir="ltr"] {
  font-family: var(--font-english);
}

/* إعدادات النصوص */
.text-arabic {
  font-family: var(--font-arabic);
  direction: rtl;
}

.text-english {
  font-family: var(--font-english);
  direction: ltr;
}

/* مكونات مخصصة */
@layer components {
  /* أزرار */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .btn-ghost {
    @apply btn border-transparent text-gray-700 bg-transparent hover:bg-gray-100 focus:ring-primary-500;
  }

  /* بطاقات */
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 overflow-hidden;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-100 bg-gray-50;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-100 bg-gray-50;
  }

  /* نماذج */
  .form-group {
    @apply mb-4;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }
  
  .form-textarea {
    @apply form-input resize-none;
  }
  
  .form-select {
    @apply form-input pr-10 bg-white;
  }
  
  .form-checkbox {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded;
  }
  
  .form-radio {
    @apply h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300;
  }
  
  .form-error {
    @apply mt-1 text-sm text-secondary-600;
  }

  /* جداول */
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-start text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-row {
    @apply hover:bg-gray-50 transition-colors duration-150;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* شارات */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .badge-gray {
    @apply badge bg-gray-100 text-gray-800;
  }

  /* تنبيهات */
  .alert {
    @apply p-4 rounded-lg border;
  }
  
  .alert-success {
    @apply alert bg-success-50 border-success-200 text-success-800;
  }
  
  .alert-warning {
    @apply alert bg-warning-50 border-warning-200 text-warning-800;
  }
  
  .alert-danger {
    @apply alert bg-secondary-50 border-secondary-200 text-secondary-800;
  }
  
  .alert-info {
    @apply alert bg-info-50 border-info-200 text-info-800;
  }

  /* تحميل */
  .loading {
    @apply inline-block animate-spin rounded-full h-4 w-4 border-b-2 border-current;
  }
  
  .loading-lg {
    @apply loading h-8 w-8;
  }

  /* فواصل */
  .divider {
    @apply border-t border-gray-200 my-6;
  }
  
  .divider-vertical {
    @apply border-r border-gray-200 mx-6;
  }
}

/* أدوات مساعدة */
@layer utilities {
  /* اتجاه النص */
  .dir-rtl {
    direction: rtl;
  }
  
  .dir-ltr {
    direction: ltr;
  }
  
  /* محاذاة النص */
  .text-start {
    text-align: start;
  }
  
  .text-end {
    text-align: end;
  }
  
  /* العوامة */
  .float-start {
    float: inline-start;
  }
  
  .float-end {
    float: inline-end;
  }
  
  /* الهوامش */
  .ms-auto {
    margin-inline-start: auto;
  }
  
  .me-auto {
    margin-inline-end: auto;
  }
  
  /* الحشو */
  .ps-0 { padding-inline-start: 0; }
  .ps-1 { padding-inline-start: 0.25rem; }
  .ps-2 { padding-inline-start: 0.5rem; }
  .ps-3 { padding-inline-start: 0.75rem; }
  .ps-4 { padding-inline-start: 1rem; }
  .ps-5 { padding-inline-start: 1.25rem; }
  .ps-6 { padding-inline-start: 1.5rem; }
  
  .pe-0 { padding-inline-end: 0; }
  .pe-1 { padding-inline-end: 0.25rem; }
  .pe-2 { padding-inline-end: 0.5rem; }
  .pe-3 { padding-inline-end: 0.75rem; }
  .pe-4 { padding-inline-end: 1rem; }
  .pe-5 { padding-inline-end: 1.25rem; }
  .pe-6 { padding-inline-end: 1.5rem; }
  
  /* ظلال مخصصة */
  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }
  
  .shadow-medium {
    box-shadow: var(--shadow-medium);
  }
  
  .shadow-strong {
    box-shadow: var(--shadow-strong);
  }
  
  /* انتقالات سلسة */
  .transition-all {
    transition: all 0.2s ease-in-out;
  }
  
  .transition-colors {
    transition: color 0.2s ease-in-out, background-color 0.2s ease-in-out, border-color 0.2s ease-in-out;
  }
  
  /* تأثيرات التمرير */
  .hover-lift {
    transition: transform 0.2s ease-in-out;
  }
  
  .hover-lift:hover {
    transform: translateY(-2px);
  }
  
  /* تدرجات مخصصة */
  .gradient-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  }
  
  .gradient-success {
    background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  }
  
  .gradient-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  }
  
  .gradient-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  }
}

/* تخصيصات شريط التمرير */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* تحسينات الطباعة */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-after: always;
  }
  
  .print-avoid-break {
    page-break-inside: avoid;
  }
}

/* تحسينات الاستجابة */
@media (max-width: 640px) {
  .mobile-full {
    width: 100% !important;
  }
  
  .mobile-hidden {
    display: none !important;
  }
}

/* تأثيرات الحركة */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateY(-10px);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromRight {
  from { 
    opacity: 0;
    transform: translateX(20px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromLeft {
  from { 
    opacity: 0;
    transform: translateX(-20px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-in {
  animation: slideIn 0.3s ease-out;
}

.animate-slide-in-right {
  animation: slideInFromRight 0.3s ease-out;
}

.animate-slide-in-left {
  animation: slideInFromLeft 0.3s ease-out;
}