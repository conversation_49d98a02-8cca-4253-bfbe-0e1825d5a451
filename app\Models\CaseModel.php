<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Scout\Searchable;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

/**
 * نموذج القضية - إدارة القضايا القانونية
 */
class CaseModel extends Model
{
    use HasFactory, SoftDeletes, Searchable, LogsActivity;

    /**
     * اسم الجدول
     */
    protected $table = 'cases';

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'case_number',
        'court_case_number',
        'title',
        'description',
        'case_type_id',
        'case_status_id',
        'priority',
        'client_id',
        'lawyer_id',
        'court_id',
        'court_chamber_id',
        'filing_date',
        'first_hearing_date',
        'expected_completion_date',
        'case_value',
        'fees',
        'court_fees',
        'legal_basis',
        'client_demands',
        'opponent_info',
        'case_parties',
        'related_laws',
        'notes',
        'is_confidential',
        'tenant_id',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'filing_date' => 'date',
        'first_hearing_date' => 'date',
        'expected_completion_date' => 'date',
        'case_value' => 'decimal:2',
        'fees' => 'decimal:2',
        'court_fees' => 'decimal:2',
        'case_parties' => 'array',
        'related_laws' => 'array',
        'is_confidential' => 'boolean',
    ];

    /**
     * إعدادات تسجيل الأنشطة
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['title', 'case_status_id', 'priority', 'lawyer_id'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * إعدادات البحث
     */
    public function toSearchableArray(): array
    {
        return [
            'case_number' => $this->case_number,
            'title' => $this->title,
            'description' => $this->description,
            'client_name' => $this->client->name ?? '',
            'lawyer_name' => $this->lawyer->name ?? '',
        ];
    }

    /**
     * العلاقة مع نوع القضية
     */
    public function caseType()
    {
        return $this->belongsTo(CaseType::class);
    }

    /**
     * العلاقة مع حالة القضية
     */
    public function caseStatus()
    {
        return $this->belongsTo(CaseStatus::class);
    }

    /**
     * العلاقة مع العميل
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * العلاقة مع المحامي المسؤول
     */
    public function lawyer()
    {
        return $this->belongsTo(User::class, 'lawyer_id');
    }

    /**
     * العلاقة مع المحكمة
     */
    public function court()
    {
        return $this->belongsTo(Court::class);
    }

    /**
     * العلاقة مع دائرة المحكمة
     */
    public function courtChamber()
    {
        return $this->belongsTo(CourtChamber::class);
    }

    /**
     * العلاقة مع الجلسات
     */
    public function hearings()
    {
        return $this->hasMany(Hearing::class, 'case_id')->orderBy('hearing_date', 'desc');
    }

    /**
     * العلاقة مع المهام
     */
    public function tasks()
    {
        return $this->hasMany(Task::class, 'case_id');
    }

    /**
     * العلاقة مع المستندات
     */
    public function documents()
    {
        return $this->hasMany(Document::class, 'case_id');
    }

    /**
     * العلاقة مع الفواتير
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class, 'case_id');
    }

    /**
     * العلاقة مع المصروفات
     */
    public function expenses()
    {
        return $this->hasMany(Expense::class, 'case_id');
    }

    /**
     * العلاقة مع تسجيل الوقت
     */
    public function timesheets()
    {
        return $this->hasMany(Timesheet::class, 'case_id');
    }

    /**
     * الحصول على الجلسة القادمة
     */
    public function nextHearing()
    {
        return $this->hearings()
            ->where('hearing_date', '>=', now()->toDateString())
            ->where('status', 'scheduled')
            ->orderBy('hearing_date')
            ->first();
    }

    /**
     * الحصول على آخر جلسة
     */
    public function lastHearing()
    {
        return $this->hearings()
            ->where('status', 'completed')
            ->first();
    }

    /**
     * الحصول على المهام المعلقة
     */
    public function pendingTasks()
    {
        return $this->tasks()->whereIn('status', ['pending', 'in_progress']);
    }

    /**
     * الحصول على إجمالي الساعات المسجلة
     */
    public function getTotalHoursAttribute(): float
    {
        return $this->timesheets()->sum('hours');
    }

    /**
     * الحصول على إجمالي الساعات القابلة للفوترة
     */
    public function getBillableHoursAttribute(): float
    {
        return $this->timesheets()->sum('billable_hours');
    }

    /**
     * الحصول على إجمالي المصروفات
     */
    public function getTotalExpensesAttribute(): float
    {
        return $this->expenses()->sum('amount');
    }

    /**
     * الحصول على إجمالي الفواتير
     */
    public function getTotalInvoicedAttribute(): float
    {
        return $this->invoices()->sum('total_amount');
    }

    /**
     * الحصول على إجمالي المدفوعات
     */
    public function getTotalPaidAttribute(): float
    {
        return $this->invoices()->sum('paid_amount');
    }

    /**
     * الحصول على الرصيد المتبقي
     */
    public function getBalanceAttribute(): float
    {
        return $this->total_invoiced - $this->total_paid;
    }

    /**
     * التحقق من كون القضية عاجلة
     */
    public function isUrgent(): bool
    {
        return $this->priority === 'urgent';
    }

    /**
     * التحقق من كون القضية مكتملة
     */
    public function isCompleted(): bool
    {
        return $this->caseStatus && $this->caseStatus->is_final;
    }

    /**
     * التحقق من كون القضية متأخرة
     */
    public function isOverdue(): bool
    {
        return $this->expected_completion_date && 
               $this->expected_completion_date->isPast() && 
               !$this->isCompleted();
    }

    /**
     * إنشاء رقم قضية تلقائي
     */
    public static function generateCaseNumber(): string
    {
        $year = now()->year;
        $lastCase = static::whereYear('created_at', $year)
            ->orderBy('id', 'desc')
            ->first();
        
        $number = $lastCase ? (int) substr($lastCase->case_number, -4) + 1 : 1;
        
        return sprintf('%d-%04d', $year, $number);
    }

    /**
     * Scope للقضايا النشطة
     */
    public function scopeActive($query)
    {
        return $query->whereHas('caseStatus', function ($q) {
            $q->where('is_final', false);
        });
    }

    /**
     * Scope للقضايا المكتملة
     */
    public function scopeCompleted($query)
    {
        return $query->whereHas('caseStatus', function ($q) {
            $q->where('is_final', true);
        });
    }

    /**
     * Scope للقضايا العاجلة
     */
    public function scopeUrgent($query)
    {
        return $query->where('priority', 'urgent');
    }
}