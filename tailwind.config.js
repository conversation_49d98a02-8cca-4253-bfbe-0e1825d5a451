import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.vue',
        './resources/js/**/*.js',
        './resources/js/**/*.ts',
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Cairo', 'Figtree', ...defaultTheme.fontFamily.sans],
                arabic: ['Cairo', 'Amiri', 'Noto Sans Arabic'],
                english: ['Inter', 'Roboto', ...defaultTheme.fontFamily.sans],
            },
            colors: {
                // ألوان منصة سندان
                primary: {
                    50: '#eff6ff',
                    100: '#dbeafe',
                    200: '#bfdbfe',
                    300: '#93c5fd',
                    400: '#60a5fa',
                    500: '#3b82f6',
                    600: '#1e40af', // اللون الأساسي
                    700: '#1d4ed8',
                    800: '#1e3a8a',
                    900: '#1e293b',
                    950: '#0f172a',
                },
                secondary: {
                    50: '#fef2f2',
                    100: '#fee2e2',
                    200: '#fecaca',
                    300: '#fca5a5',
                    400: '#f87171',
                    500: '#ef4444',
                    600: '#dc2626', // أحمر للعاجل
                    700: '#b91c1c',
                    800: '#991b1b',
                    900: '#7f1d1d',
                    950: '#450a0a',
                },
                success: {
                    50: '#f0fdf4',
                    100: '#dcfce7',
                    200: '#bbf7d0',
                    300: '#86efac',
                    400: '#4ade80',
                    500: '#22c55e',
                    600: '#16a34a', // أخضر للمكتمل
                    700: '#15803d',
                    800: '#166534',
                    900: '#14532d',
                    950: '#052e16',
                },
                warning: {
                    50: '#fffbeb',
                    100: '#fef3c7',
                    200: '#fde68a',
                    300: '#fcd34d',
                    400: '#fbbf24',
                    500: '#f59e0b',
                    600: '#d97706', // برتقالي للتحذير
                    700: '#b45309',
                    800: '#92400e',
                    900: '#78350f',
                    950: '#451a03',
                },
                info: {
                    50: '#f0f9ff',
                    100: '#e0f2fe',
                    200: '#bae6fd',
                    300: '#7dd3fc',
                    400: '#38bdf8',
                    500: '#0ea5e9',
                    600: '#0891b2', // أزرق فاتح للمعلومات
                    700: '#0e7490',
                    800: '#155e75',
                    900: '#164e63',
                    950: '#0c2d36',
                },
                gold: {
                    50: '#fefce8',
                    100: '#fef9c3',
                    200: '#fef08a',
                    300: '#fde047',
                    400: '#facc15',
                    500: '#eab308',
                    600: '#d4af37', // ذهبي للعضوية المميزة
                    700: '#a16207',
                    800: '#854d0e',
                    900: '#713f12',
                    950: '#422006',
                },
                dark: {
                    50: '#f8fafc',
                    100: '#f1f5f9',
                    200: '#e2e8f0',
                    300: '#cbd5e1',
                    400: '#94a3b8',
                    500: '#64748b',
                    600: '#475569',
                    700: '#334155',
                    800: '#1e293b',
                    900: '#1f2937', // رمادي داكن
                    950: '#0f172a',
                },
            },
            spacing: {
                '18': '4.5rem',
                '88': '22rem',
                '128': '32rem',
            },
            borderRadius: {
                '4xl': '2rem',
            },
            boxShadow: {
                'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
                'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
                'strong': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 4px 25px -5px rgba(0, 0, 0, 0.1)',
            },
            animation: {
                'fade-in': 'fadeIn 0.5s ease-in-out',
                'slide-in': 'slideIn 0.3s ease-out',
                'bounce-soft': 'bounceSoft 2s infinite',
                'pulse-soft': 'pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            },
            keyframes: {
                fadeIn: {
                    '0%': { opacity: '0' },
                    '100%': { opacity: '1' },
                },
                slideIn: {
                    '0%': { transform: 'translateY(-10px)', opacity: '0' },
                    '100%': { transform: 'translateY(0)', opacity: '1' },
                },
                bounceSoft: {
                    '0%, 100%': {
                        transform: 'translateY(-5%)',
                        animationTimingFunction: 'cubic-bezier(0.8, 0, 1, 1)',
                    },
                    '50%': {
                        transform: 'translateY(0)',
                        animationTimingFunction: 'cubic-bezier(0, 0, 0.2, 1)',
                    },
                },
                pulseSoft: {
                    '0%, 100%': { opacity: '1' },
                    '50%': { opacity: '0.8' },
                },
            },
            screens: {
                'xs': '475px',
                '3xl': '1600px',
            },
            zIndex: {
                '60': '60',
                '70': '70',
                '80': '80',
                '90': '90',
                '100': '100',
            },
        },
    },

    plugins: [
        forms,
        typography,
        // إضافة دعم RTL
        function({ addUtilities, addBase, theme }) {
            addBase({
                'html': {
                    direction: 'rtl',
                },
                'html[dir="ltr"]': {
                    direction: 'ltr',
                },
                'body': {
                    fontFamily: theme('fontFamily.arabic'),
                },
                'body[dir="ltr"]': {
                    fontFamily: theme('fontFamily.english'),
                },
            });

            addUtilities({
                '.rtl': {
                    direction: 'rtl',
                },
                '.ltr': {
                    direction: 'ltr',
                },
                '.text-start': {
                    'text-align': 'start',
                },
                '.text-end': {
                    'text-align': 'end',
                },
                '.float-start': {
                    'float': 'inline-start',
                },
                '.float-end': {
                    'float': 'inline-end',
                },
                '.ms-auto': {
                    'margin-inline-start': 'auto',
                },
                '.me-auto': {
                    'margin-inline-end': 'auto',
                },
                '.ps-0': { 'padding-inline-start': '0' },
                '.ps-1': { 'padding-inline-start': '0.25rem' },
                '.ps-2': { 'padding-inline-start': '0.5rem' },
                '.ps-3': { 'padding-inline-start': '0.75rem' },
                '.ps-4': { 'padding-inline-start': '1rem' },
                '.ps-5': { 'padding-inline-start': '1.25rem' },
                '.ps-6': { 'padding-inline-start': '1.5rem' },
                '.pe-0': { 'padding-inline-end': '0' },
                '.pe-1': { 'padding-inline-end': '0.25rem' },
                '.pe-2': { 'padding-inline-end': '0.5rem' },
                '.pe-3': { 'padding-inline-end': '0.75rem' },
                '.pe-4': { 'padding-inline-end': '1rem' },
                '.pe-5': { 'padding-inline-end': '1.25rem' },
                '.pe-6': { 'padding-inline-end': '1.5rem' },
            });
        },
    ],

    darkMode: 'class',
};