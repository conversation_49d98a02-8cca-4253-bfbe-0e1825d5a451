<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول القضايا
     */
    public function up(): void
    {
        Schema::create('cases', function (Blueprint $table) {
            $table->id();
            $table->string('case_number')->unique(); // رقم القضية
            $table->string('court_case_number')->nullable(); // رقم القضية في المحكمة
            $table->string('title'); // عنوان القضية
            $table->text('description'); // وصف القضية
            $table->foreignId('case_type_id')->constrained()->onDelete('restrict');
            $table->foreignId('case_status_id')->constrained()->onDelete('restrict');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->foreignId('client_id')->constrained()->onDelete('restrict');
            $table->foreignId('lawyer_id')->constrained('users')->onDelete('restrict'); // المحامي المسؤول
            $table->foreignId('court_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('court_chamber_id')->nullable()->constrained()->onDelete('set null');
            $table->date('filing_date')->nullable(); // تاريخ رفع القضية
            $table->date('first_hearing_date')->nullable(); // تاريخ أول جلسة
            $table->date('expected_completion_date')->nullable(); // التاريخ المتوقع للانتهاء
            $table->decimal('case_value', 15, 2)->nullable(); // قيمة القضية
            $table->decimal('fees', 10, 2)->nullable(); // الأتعاب
            $table->decimal('court_fees', 10, 2)->nullable(); // رسوم المحكمة
            $table->text('legal_basis')->nullable(); // الأساس القانوني
            $table->text('client_demands')->nullable(); // مطالب العميل
            $table->text('opponent_info')->nullable(); // معلومات الخصم
            $table->json('case_parties')->nullable(); // أطراف القضية
            $table->json('related_laws')->nullable(); // القوانين ذات الصلة
            $table->text('notes')->nullable();
            $table->boolean('is_confidential')->default(false); // سرية القضية
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['tenant_id', 'case_status_id']);
            $table->index(['tenant_id', 'lawyer_id']);
            $table->index(['tenant_id', 'client_id']);
            $table->index(['tenant_id', 'priority']);
            $table->index(['tenant_id', 'filing_date']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('cases');
    }
};