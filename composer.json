{"name": "sanadan/law-platform", "type": "project", "description": "منصة سندان لإدارة مكاتب المحاماة - SaaS Platform for Law Firms", "keywords": ["laravel", "law", "legal", "saas", "kuwait"], "license": "MIT", "require": {"php": "^8.3", "laravel/framework": "^11.0", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "stancl/tenancy": "^3.8", "laravel/scout": "^10.0", "meilisearch/meilisearch-php": "^1.0", "barryvdh/laravel-dompdf": "^2.0", "spatie/laravel-permission": "^6.0", "spatie/laravel-activitylog": "^4.8", "league/flysystem-aws-s3-v3": "^3.0", "stripe/stripe-php": "^13.0", "pusher/pusher-php-server": "^7.2", "laravel/horizon": "^5.21"}, "require-dev": {"fakerphp/faker": "^1.23", "laravel/pint": "^1.13", "laravel/sail": "^1.26", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "phpunit/phpunit": "^11.0", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}