<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول المستندات
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم المستند
            $table->string('original_name'); // الاسم الأصلي للملف
            $table->text('description')->nullable(); // وصف المستند
            $table->string('file_path'); // مسار الملف
            $table->string('file_type'); // نوع الملف
            $table->string('mime_type'); // نوع MIME
            $table->bigInteger('file_size'); // حجم الملف بالبايت
            $table->string('file_hash')->nullable(); // هاش الملف للتحقق من التكامل
            $table->enum('category', [
                'contract', // عقد
                'power_of_attorney', // توكيل
                'court_document', // مستند محكمة
                'evidence', // دليل
                'correspondence', // مراسلات
                'identification', // هوية
                'financial', // مالي
                'legal_opinion', // رأي قانوني
                'judgment', // حكم
                'appeal', // استئناف
                'other' // أخرى
            ])->default('other');
            $table->enum('confidentiality', [
                'public', // عام
                'internal', // داخلي
                'confidential', // سري
                'highly_confidential' // سري للغاية
            ])->default('internal');
            $table->foreignId('case_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->json('tags')->nullable(); // علامات للتصنيف
            $table->json('metadata')->nullable(); // بيانات وصفية إضافية
            $table->boolean('is_signed')->default(false); // موقع إلكترونياً
            $table->json('signatures')->nullable(); // معلومات التوقيعات
            $table->boolean('is_template')->default(false); // قالب مستند
            $table->integer('version')->default(1); // إصدار المستند
            $table->foreignId('parent_document_id')->nullable()->constrained('documents')->onDelete('cascade'); // المستند الأصلي
            $table->date('expiry_date')->nullable(); // تاريخ انتهاء الصلاحية
            $table->boolean('is_archived')->default(false); // مؤرشف
            $table->datetime('archived_at')->nullable(); // تاريخ الأرشفة
            $table->text('ocr_content')->nullable(); // النص المستخرج بـ OCR
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['case_id', 'category']);
            $table->index(['client_id', 'category']);
            $table->index(['tenant_id', 'uploaded_by']);
            $table->index(['tenant_id', 'is_template']);
            $table->index(['tenant_id', 'is_archived']);
            $table->fullText(['name', 'description', 'ocr_content']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};