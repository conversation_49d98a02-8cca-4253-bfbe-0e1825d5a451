<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول تسجيل الوقت
     */
    public function up(): void
    {
        Schema::create('timesheets', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('case_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('task_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('client_id')->nullable()->constrained()->onDelete('set null');
            $table->date('date'); // تاريخ العمل
            $table->time('start_time')->nullable(); // وقت البداية
            $table->time('end_time')->nullable(); // وقت النهاية
            $table->decimal('hours', 5, 2); // عدد الساعات
            $table->decimal('billable_hours', 5, 2)->default(0); // الساعات القابلة للفوترة
            $table->decimal('hourly_rate', 8, 2)->nullable(); // السعر بالساعة
            $table->decimal('total_amount', 10, 2)->default(0); // المبلغ الإجمالي
            $table->enum('activity_type', [
                'research', // بحث
                'drafting', // صياغة
                'review', // مراجعة
                'meeting', // اجتماع
                'phone_call', // مكالمة هاتفية
                'email', // بريد إلكتروني
                'court_appearance', // حضور محكمة
                'travel', // سفر
                'administrative', // إدارية
                'consultation', // استشارة
                'negotiation', // تفاوض
                'other' // أخرى
            ])->default('other');
            $table->text('description'); // وصف العمل المنجز
            $table->text('notes')->nullable(); // ملاحظات إضافية
            $table->boolean('is_billable')->default(true); // قابل للفوترة
            $table->boolean('is_approved')->default(false); // معتمد
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->datetime('approved_at')->nullable(); // تاريخ الاعتماد
            $table->boolean('is_invoiced')->default(false); // تم فوترته
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');
            $table->enum('status', [
                'draft', // مسودة
                'submitted', // مقدم
                'approved', // معتمد
                'rejected', // مرفوض
                'invoiced' // مفوتر
            ])->default('draft');
            $table->json('break_times')->nullable(); // أوقات الاستراحة
            $table->boolean('is_overtime')->default(false); // وقت إضافي
            $table->decimal('overtime_multiplier', 3, 2)->default(1.5); // مضاعف الوقت الإضافي
            $table->string('location')->nullable(); // موقع العمل
            $table->json('tags')->nullable(); // علامات
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['user_id', 'date']);
            $table->index(['case_id', 'date']);
            $table->index(['tenant_id', 'is_billable']);
            $table->index(['tenant_id', 'is_approved']);
            $table->index(['tenant_id', 'status']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('timesheets');
    }
};