<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول دوائر المحاكم
     */
    public function up(): void
    {
        Schema::create('court_chambers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('court_id')->constrained()->onDelete('cascade');
            $table->string('name'); // اسم الدائرة
            $table->string('chamber_number')->nullable(); // رقم الدائرة
            $table->string('judge_name'); // اسم القاضي
            $table->string('judge_title')->nullable(); // لقب القاضي
            $table->string('clerk_name')->nullable(); // اسم كاتب المحكمة
            $table->enum('specialization', [
                'civil', // مدني
                'commercial', // تجاري
                'criminal', // جنائي
                'family', // أسرة
                'administrative', // إداري
                'labor', // عمالي
                'real_estate', // عقاري
                'inheritance', // مواريث
                'general' // عام
            ])->default('general');
            $table->json('working_days')->nullable(); // أيام العمل
            $table->time('session_start_time')->nullable(); // وقت بداية الجلسة
            $table->time('session_end_time')->nullable(); // وقت نهاية الجلسة
            $table->text('notes')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('tenant_id');
            $table->timestamps();

            $table->index('tenant_id');
            $table->index(['court_id', 'is_active']);
            $table->index(['tenant_id', 'specialization']);
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('court_chambers');
    }
};