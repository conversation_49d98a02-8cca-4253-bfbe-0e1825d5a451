# Laravel
/node_modules
/public/build
/public/hot
/public/storage
/storage/*.key
/vendor
.env
.env.backup
.env.production
.phpunit.result.cache
Homestead.json
Homestead.yaml
auth.json
npm-debug.log
yarn-error.log
/.fleet
/.idea
/.vscode

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Laravel specific
bootstrap/compiled.php
app/storage/
public/packages
public/storage
public/hot

# Composer
composer.phar
/vendor/

# Package Files
*.7z
*.dmg
*.gz
*.iso
*.jar
*.rar
*.tar
*.zip

# Database
*.sqlite
*.sqlite3

# Backup files
*.bak
*.backup
*.old

# Cache
.cache/
*.cache

# Build files
/build/
/dist/

# Documentation
/docs/build/

# Test files
/tests/coverage/
/coverage/

# IDE files
*.swp
*.swo
*~

# Laravel Telescope
/storage/telescope/

# Laravel Horizon
/storage/horizon/

# Laravel Debugbar
/storage/debugbar/

# Spatie Laravel Permission
/storage/app/spatie-permission/

# Custom uploads (if not using cloud storage)
/storage/app/public/uploads/
/storage/app/public/documents/
/storage/app/public/avatars/

# Backup files
/storage/app/backups/

# Log files
/storage/logs/*.log

# Session files
/storage/framework/sessions/*

# Cache files
/storage/framework/cache/data/*
/storage/framework/views/*

# Compiled views
/storage/framework/views/*.php

# Route cache
/bootstrap/cache/routes-*.php

# Config cache
/bootstrap/cache/config.php

# Services cache
/bootstrap/cache/services.php

# Packages cache
/bootstrap/cache/packages.php

# Events cache
/bootstrap/cache/events.php

# Translation cache
/bootstrap/cache/translations.php

# Livewire tmp
/storage/app/livewire-tmp/

# Sanctum personal access tokens
/storage/app/sanctum/

# Multi-tenancy databases
/storage/tenancy/

# Custom tenant databases (if using file-based storage)
/database/tenants/

# API documentation
/storage/api-docs/

# Generated files
/public/js/
/public/css/
/public/mix-manifest.json

# Hot reload
/public/hot

# Build artifacts
/public/build/

# Webpack
webpack-stats.json

# Parcel
.parcel-cache/

# Vite
/public/vite.config.js.timestamp-*

# TypeScript
*.tsbuildinfo

# ESLint
.eslintcache

# Prettier
.prettierignore

# Stylelint
.stylelintcache

# Jest
/coverage/

# Cypress
/cypress/videos/
/cypress/screenshots/

# Playwright
/test-results/
/playwright-report/
/playwright/.cache/

# Storybook
/storybook-static/

# Temporary files
*.tmp
*.temp

# Lock files (keep yarn.lock or package-lock.json, not both)
# package-lock.json
# yarn.lock

# Local development
.local/
.dev/

# Docker
.docker/
docker-compose.override.yml

# Kubernetes
*.kubeconfig

# Terraform
*.tfstate
*.tfstate.*
.terraform/

# Ansible
*.retry

# Vagrant
.vagrant/

# Local SSL certificates
*.pem
*.key
*.crt
*.csr

# Local configuration
.local.env
.development.env

# Backup and temporary files
*.backup
*.tmp
*.temp
*.old
*.orig

# macOS
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*