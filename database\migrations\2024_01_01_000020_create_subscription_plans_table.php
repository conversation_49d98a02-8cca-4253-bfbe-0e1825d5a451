<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * تشغيل Migration - إنشاء جدول خطط الاشتراك
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // اسم الخطة
            $table->string('name_en')->nullable(); // الاسم بالإنجليزية
            $table->string('slug')->unique(); // معرف الخطة
            $table->text('description')->nullable(); // وصف الخطة
            $table->text('description_en')->nullable(); // الوصف بالإنجليزية
            $table->decimal('price', 8, 2); // السعر
            $table->string('currency', 3)->default('KWD'); // العملة
            $table->enum('billing_cycle', [
                'monthly', // شهري
                'quarterly', // ربع سنوي
                'semi_annual', // نصف سنوي
                'annual', // سنوي
                'lifetime' // مدى الحياة
            ])->default('monthly');
            $table->integer('trial_days')->default(0); // أيام التجربة المجانية
            $table->json('features'); // الميزات المتاحة
            $table->json('limits'); // الحدود والقيود
            $table->integer('max_users')->default(1); // عدد المستخدمين الأقصى
            $table->integer('max_cases')->default(100); // عدد القضايا الأقصى
            $table->integer('max_clients')->default(100); // عدد العملاء الأقصى
            $table->bigInteger('max_storage_gb')->default(5); // مساحة التخزين بالجيجابايت
            $table->boolean('has_api_access')->default(false); // الوصول للـ API
            $table->boolean('has_custom_branding')->default(false); // العلامة التجارية المخصصة
            $table->boolean('has_advanced_reports')->default(false); // التقارير المتقدمة
            $table->boolean('has_ai_features')->default(false); // ميزات الذكاء الاصطناعي
            $table->boolean('has_priority_support')->default(false); // الدعم المتقدم
            $table->integer('sort_order')->default(0); // ترتيب العرض
            $table->boolean('is_popular')->default(false); // خطة شائعة
            $table->boolean('is_active')->default(true); // نشطة
            $table->boolean('is_visible')->default(true); // مرئية للعملاء
            $table->string('stripe_price_id')->nullable(); // معرف السعر في Stripe
            $table->string('paypal_plan_id')->nullable(); // معرف الخطة في PayPal
            $table->json('metadata')->nullable(); // بيانات إضافية
            $table->timestamps();

            $table->index(['is_active', 'is_visible']);
            $table->index('sort_order');
        });
    }

    /**
     * التراجع عن Migration
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};