<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Activitylog\Traits\LogsActivity;
use Spatie\Activitylog\LogOptions;

/**
 * نموذج المستخدم - إدارة المستخدمين في النظام
 */
class User extends Authenticatable implements MustVerifyEmail
{
    use HasApiTokens, HasFactory, Notifiable, LogsActivity;

    /**
     * الحقول القابلة للتعبئة
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'avatar',
        'status',
        'role',
        'tenant_id',
        'settings',
        'last_login_at',
        'two_factor_secret',
        'two_factor_recovery_codes',
        'two_factor_confirmed_at',
    ];

    /**
     * الحقول المخفية
     */
    protected $hidden = [
        'password',
        'remember_token',
        'two_factor_secret',
        'two_factor_recovery_codes',
    ];

    /**
     * تحويل الحقول إلى أنواع البيانات المناسبة
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
        'settings' => 'array',
        'last_login_at' => 'datetime',
        'two_factor_confirmed_at' => 'datetime',
    ];

    /**
     * إعدادات تسجيل الأنشطة
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly(['name', 'email', 'role', 'status'])
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }

    /**
     * العلاقة مع مكتب المحاماة (للمحامين)
     */
    public function lawyer()
    {
        return $this->hasOne(Lawyer::class);
    }

    /**
     * العلاقة مع القضايا المسندة للمحامي
     */
    public function assignedCases()
    {
        return $this->hasMany(CaseModel::class, 'lawyer_id');
    }

    /**
     * العلاقة مع المهام المسندة للمستخدم
     */
    public function assignedTasks()
    {
        return $this->hasMany(Task::class, 'assigned_to');
    }

    /**
     * العلاقة مع المهام التي أنشأها المستخدم
     */
    public function createdTasks()
    {
        return $this->hasMany(Task::class, 'created_by');
    }

    /**
     * العلاقة مع تسجيل الوقت
     */
    public function timesheets()
    {
        return $this->hasMany(Timesheet::class);
    }

    /**
     * العلاقة مع المستندات المرفوعة
     */
    public function uploadedDocuments()
    {
        return $this->hasMany(Document::class, 'uploaded_by');
    }

    /**
     * العلاقة مع الفواتير المنشأة
     */
    public function createdInvoices()
    {
        return $this->hasMany(Invoice::class, 'created_by');
    }

    /**
     * العلاقة مع المصروفات
     */
    public function expenses()
    {
        return $this->hasMany(Expense::class);
    }

    /**
     * التحقق من دور المستخدم
     */
    public function hasRole(string $role): bool
    {
        return $this->role === $role;
    }

    /**
     * التحقق من كون المستخدم محامي
     */
    public function isLawyer(): bool
    {
        return $this->hasRole('lawyer');
    }

    /**
     * التحقق من كون المستخدم مدير
     */
    public function isAdmin(): bool
    {
        return $this->hasRole('admin');
    }

    /**
     * التحقق من كون المستخدم موظف
     */
    public function isEmployee(): bool
    {
        return $this->hasRole('employee');
    }

    /**
     * التحقق من كون المستخدم عميل
     */
    public function isClient(): bool
    {
        return $this->hasRole('client');
    }

    /**
     * التحقق من حالة المستخدم النشطة
     */
    public function isActive(): bool
    {
        return $this->status === 'active';
    }

    /**
     * الحصول على الاسم الكامل مع اللقب
     */
    public function getFullNameAttribute(): string
    {
        if ($this->isLawyer() && $this->lawyer) {
            return "المحامي {$this->name}";
        }
        
        return $this->name;
    }

    /**
     * الحصول على الصورة الشخصية أو الافتراضية
     */
    public function getAvatarUrlAttribute(): string
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }
        
        return "https://ui-avatars.com/api/?name=" . urlencode($this->name) . "&color=7F9CF5&background=EBF4FF";
    }

    /**
     * تحديث وقت آخر تسجيل دخول
     */
    public function updateLastLogin(): void
    {
        $this->update(['last_login_at' => now()]);
    }

    /**
     * الحصول على إعداد معين
     */
    public function getSetting(string $key, $default = null)
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * تحديث إعداد معين
     */
    public function updateSetting(string $key, $value): void
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);
        $this->update(['settings' => $settings]);
    }
}